'use client'

import React, { useState } from 'react';
import Konva from 'konva';
import { Image as ImageIcon } from 'lucide-react';
import toast from 'react-hot-toast';
import Image from 'next/image';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import {
  projectImageStorage, ImageMetadata,
} from '@/common/utils/projectImageStorage';
import { CircularSpinner } from '../../../atoms';
import { addCursorHandlers } from '../CanvasEditor';

interface RecentUploadsPanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
}

const scaleImageToFitCanvas = (
  img: Konva.Image,
  canvas: Konva.Stage,
  containerRef?: React.RefObject<HTMLDivElement>,
  zoomLevel?: number,
) => {

  if (containerRef?.current && zoomLevel) {
    const canvasWidth = canvas.width();
    const canvasHeight = canvas.height();
    const container = containerRef.current!;
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    const scaleX = containerWidth / canvasWidth;
    const scaleY = containerHeight / canvasHeight;

    const scale = Math.min(scaleX + zoomLevel, scaleY + .1 + zoomLevel + .1, 1.3);

    img.x(0);
    img.y(0);
    img.scaleX(scale);
    img.scaleY(scale);
    img.draggable(true);

    addCursorHandlers(img);

    let layer = canvas.findOne('Layer') as Konva.Layer;
    if (!layer) {
      layer = new Konva.Layer();
      canvas.add(layer);
    }

    layer.add(img);

    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }

    transformer.nodes([img]);
    canvas.batchDraw();
  }
};

export const RecentUploadsPanel = ({
  canvas,
  containerRef,
  zoomLevel,
}: RecentUploadsPanelProps) => {
  const [generatedImages, setGeneratedImages] = useState<ImageMetadata[]>([]);
  const [uploadedImages, setUploadedImages] = useState<ImageMetadata[]>([]);
  const [creationImages, setCreationImages] = useState<ImageMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingImages, setIsFetchingImages] = useState(false);
  const { activeProject } = useProjectContext();

  const fetchRecentImages = React.useCallback(async () => {
    if (!activeProject?.project_id) {
      setGeneratedImages([]);
      setUploadedImages([]);
      setCreationImages([]);
      return;
    }

    setIsFetchingImages(true);
    try {

      const projectImages = await projectImageStorage.getRecentImages(activeProject.project_id);
      setGeneratedImages(projectImages.generated);
      setUploadedImages(projectImages.uploaded);
      setCreationImages(projectImages.creations || []);
    } catch (error) {
      console.error('Error fetching recent images:', error);
    } finally {
      setIsFetchingImages(false);
    }
  }, [activeProject?.project_id]);

  React.useEffect(() => {
    fetchRecentImages();
  }, [fetchRecentImages]);

  React.useEffect(() => {
    const handleProjectImagesUpdate = (event: CustomEvent) => {
      if (event.detail?.projectId === activeProject?.project_id) {
        fetchRecentImages();
      }
    };

    window.addEventListener('projectImagesUpdated', handleProjectImagesUpdate as EventListener);

    return () => {
      window.removeEventListener('projectImagesUpdated', handleProjectImagesUpdate as EventListener);
    };
  }, [activeProject?.project_id, fetchRecentImages]);

  const addImageToCanvas = (imageUrl: string, imageName: string) => {
    if (!canvas) {
      return;
    }
    setIsLoading(true);

    const imageObj = new window.Image();
    imageObj.crossOrigin = 'anonymous';
    imageObj.onload = () => {
      const konvaImage = new Konva.Image({
        image: imageObj,
      });
      scaleImageToFitCanvas(konvaImage, canvas, containerRef, zoomLevel);
      toast.success(`${imageName} added to canvas!`);
      setIsLoading(false);
    };
    imageObj.onerror = () => {
      setIsLoading(false);
      toast.error('Failed to load image');
    };
    imageObj.src = imageUrl;
  };

  const addImageFromMetadata = (imageData: ImageMetadata) => {
    addImageToCanvas(imageData.filePath, imageData.fileName || 'Image');
  };

  const clearRecentImages = async () => {
    if (!activeProject?.project_id) {
      return;
    }

    setIsFetchingImages(true);
    try {
      await projectImageStorage.clearProjectImages(activeProject.project_id);
      setGeneratedImages([]);
      setUploadedImages([]);
      setCreationImages([]);
      toast.success('Recent images cleared!');
    } catch (error) {
      console.error('Error clearing recent images:', error);
      toast.error('Failed to clear recent images');
    } finally {
      setIsFetchingImages(false);
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const totalImages = generatedImages.length + uploadedImages.length + creationImages.length;

  return (
    <div className="py-4 px-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-white font-semibold text-lg">Recent Images</h3>
          {totalImages > 0 && (
            <button
              onClick={clearRecentImages}
              className="text-xs text-gray-400 hover:text-red-400 transition-colors"
            >
              Clear all
            </button>
          )}
        </div>
        <p className="text-gray-400 text-sm">Generated, uploaded, and created images for this project</p>
      </div>

      {!activeProject ? (
        <div className="text-center py-12">
          <div className="text-violets-are-blue text-sm">Loading project...</div>
        </div>
      ) : isFetchingImages ? (
        <div className="text-center py-12">
          <div className="flex flex-col items-center gap-3">
            <CircularSpinner />
            <div className="text-white text-sm">Loading recent images...</div>
          </div>
        </div>
      ) : totalImages === 0 ? (
        <div className="text-gray-400 text-sm text-center py-12">
          <ImageIcon className="mx-auto mb-3 text-gray-500" size={32} />
          <p>No recent images</p>
          <p className="text-xs mt-1">Generate or upload images to see them here</p>
        </div>
      ) : (
        <div className="space-y-6">
          {creationImages.length > 0 && (
            <div>
              <h4 className="text-gray-300 text-sm font-semibold mb-3 border-b border-neutral-700 pb-2">
                Recent Creations ({creationImages.length})
              </h4>
              <div className="space-y-3">
                {creationImages.map((image) => (
                  <div
                    key={image.id}
                    className="bg-neutral-800 rounded-lg p-3 border border-neutral-700 hover:border-violets-are-blue transition-colors cursor-pointer group"
                    onClick={() => addImageFromMetadata(image)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-neutral-700 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={image.filePath}
                          alt={image.fileName || 'Canvas creation'}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-medium truncate group-hover:text-violets-are-blue transition-colors">
                          {image.description || image.fileName || 'Canvas Creation'}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {formatDate(new Date(image.createdAt).getTime())}
                        </p>
                      </div>
                      <div className="text-gray-400 group-hover:text-violets-are-blue transition-colors">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {generatedImages.length > 0 && (
            <div>
              <h4 className="text-gray-300 text-sm font-semibold mb-3 border-b border-neutral-700 pb-2">
                Generated Images ({generatedImages.length})
              </h4>
              <div className="space-y-3">
                {generatedImages.map((image) => (
                  <div
                    key={image.id}
                    className="bg-neutral-800 rounded-lg p-3 border border-neutral-700 hover:border-violets-are-blue transition-colors cursor-pointer group"
                    onClick={() => addImageFromMetadata(image)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-neutral-700 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={image.filePath}
                          alt={image.fileName || 'Generated image'}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-medium truncate group-hover:text-violets-are-blue transition-colors">
                          {image.description || image.fileName || 'Generated Image'}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {formatDate(new Date(image.createdAt).getTime())}
                        </p>
                      </div>
                      <div className="text-gray-400 group-hover:text-violets-are-blue transition-colors">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {uploadedImages.length > 0 && (
            <div>
              <h4 className="text-gray-300 text-sm font-semibold mb-3 border-b border-neutral-700 pb-2">
                Uploaded Images ({uploadedImages.length})
              </h4>
              <div className="space-y-3">
                {uploadedImages.map((image) => (
                  <div
                    key={image.id}
                    className="bg-neutral-800 rounded-lg p-3 border border-neutral-700 hover:border-violets-are-blue transition-colors cursor-pointer group"
                    onClick={() => addImageFromMetadata(image)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-neutral-700 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={image.filePath}
                          alt={image.fileName || 'Uploaded image'}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-medium truncate group-hover:text-violets-are-blue transition-colors">
                          {image.fileName || 'Uploaded Image'}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {formatDate(new Date(image.createdAt).getTime())}
                        </p>
                      </div>
                      <div className="text-gray-400 group-hover:text-violets-are-blue transition-colors">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {isLoading && (
        <div className="text-center py-4">
          <div className="text-violets-are-blue text-sm">Adding image to canvas...</div>
        </div>
      )}

      <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-lg mt-6">
        <p className="mb-1">💡 <strong>Tips:</strong></p>
        <p>• Click any image to add it to the canvas</p>
        <p>• Recent images are stored per project</p>
        <p>• Only the last 20 images per category are kept</p>
      </div>
    </div>
  );
};
