@tailwind base;
@tailwind components;
@tailwind utilities;
@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}
html,
body {
  height: 100%;
}

html {
  scroll-behavior: smooth;
}

::-webkit-scrollbar {
   	width: 4px;
	height: 4px;
	background-color: #141420;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
  background-color: #181926;
}

::-webkit-scrollbar-thumb {
  background-color: #666666;
  border-radius: 4px;
}

.no-visible-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.no-visible-scrollbar::-webkit-scrollbar {
  display: none;
}

.buttonWithGradient defs stop:first-child {
  stop-color: white;
}
.buttonWithGradient defs stop:last-child {
  stop-color: white;
}
.buttonWithGradient:hover defs stop:first-child, .activeButton defs stop:first-child {
  stop-color: #FE8989;
}
.buttonWithGradient:hover defs stop:last-child, .activeButton defs stop:last-child {
  stop-color: #5606FF;
}

.uploadIconWithGradient defs stop:first-child {
  stop-color: #9ca3af;
}
.uploadIconWithGradient defs stop:last-child {
  stop-color: #9ca3af;
}
.uploadIconWithGradient:hover defs stop:first-child {
  stop-color: #FFF;
}
.uploadIconWithGradient:hover defs stop:last-child {
  stop-color: #FFF;
}

.gradientText{
  background-size: 200% 200%;
}

@media (min-width: 768px) {
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }
}

.container {
    max-width: 100%;
}

